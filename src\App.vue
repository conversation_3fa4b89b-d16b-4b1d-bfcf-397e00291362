<script setup lang="ts">
  import DevTool from '@/components/DevTool.vue'
  import { RouterView } from 'vue-router'
  import { useDeviceStore } from '@/stores/device'
  import { onMounted, onUnmounted } from 'vue'

  const showDevtool =
    import.meta.env.MODE === 'test' ||
    import.meta.env.MODE === 'development' ||
    import.meta.env.VITE_APP_ENV

  const deviceStore = useDeviceStore()

  // 在应用启动时初始化设备
  onMounted(() => {
    deviceStore.initDevice()
  })

  // 在应用销毁时停止心跳
  onUnmounted(() => {
    deviceStore.stopHeartbeat()
  })
</script>

<template>
  <router-view v-slot="{ Component }">
    <keep-alive v-if="$route.meta.keepAlive">
      <component :is="Component" />
    </keep-alive>
    <component
      :is="Component"
      v-if="!$route.meta.keepAlive"
    />
  </router-view>
  <DevTool v-if="showDevtool"></DevTool>
</template>

<style scoped>
  @import '@/styles/custom-theme.css';
</style>
