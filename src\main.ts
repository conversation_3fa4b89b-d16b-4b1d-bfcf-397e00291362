import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import { enablePayloadEncode, set } from 'uniplat-sdk/build/main/helpers/crypto'
import { getUniplatKey } from './encrypt'
import { antiDebugger } from './core-ui/controller/debugger'
import VConsole from 'vconsole'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

const key = getUniplatKey()

// 开启反参加密
if (import.meta.env.VITE_APP_responseEncoder && key) {
  antiDebugger()
  set(key)
  // 开启入参加密
  import.meta.env.VITE_APP_requestEncoder && enablePayloadEncode()
}

// 测试环境才开启VConsole
if (import.meta.env.MODE === 'test' || import.meta.env.MODE) {
  new VConsole()
}

// Element Plus
import ElementPlus from 'element-plus'

// 导入Element Plus CSS变量主题
import 'element-plus/dist/index.css'
import './styles/element-variables.css'
import './styles/custom-theme.css'
// import 'element-plus/theme-chalk/el-message.css'

// 路由
import router from './router'

// 状态管理
import pinia from './stores'

const app = createApp(App)

// 注册Element Plus
app.use(ElementPlus, {
  locale: zhCn
})

app.use(router)
app.use(pinia)

app.mount('#app')

console.log('import.meta.env', import.meta.env)
