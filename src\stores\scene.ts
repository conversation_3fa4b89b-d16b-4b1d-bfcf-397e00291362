import { getCacheServiceRegion } from '@/utils'
import { localKeys } from '@/utils/const'
import { sdk } from '@/utils/sdk'
import { defineStore } from 'pinia'
import { SceneTypes } from 'uniplat-sdk'
import { computed, ref } from 'vue'

export const useSceneStore = defineStore('scene', () => {
  const sceneName = import.meta.env.VITE_APP_SCENE_NAME
  const sceneList = ref<SceneTypes.SceneData[] | null>(null)
  const loading = ref<boolean>(false)

  const currentSceneData = computed(() => {
    // 没有可用场景
    if (!sceneList.value?.length) return null

    // 本地储存获取场景id
    let id = getCacheServiceRegion()
    if (id) {
      const scene = sceneList.value.find(item => item.key === id)
      if (scene) {
        sdk.setSdkScenes([{ name: sceneName, key: id }], '网格员入口')
        return scene
      }
    }

    // 返回第一个默认场景
    if (sceneList.value.length > 0) {
      sdk.uniplatSdk.global.setItem(localKeys.GridUserServiceRegionCacheKey, sceneList.value[0].key)
      sdk.setSdkScenes([{ name: sceneName, key: sceneList.value[0].key }], '网格员入口')
      return sceneList.value[0]
    }
    return null
  })

  /**
   * 加载当前可用的场景列表
   */
  const fetchSceneList = async () => {
    try {
      loading.value = true
      const res = await sdk.uniplatSdk.scene().loadList({
        scene: sceneName,
        keyword: '',
        itemIndex: 0,
        itemSize: 10,
        parent: ''
      })

      sceneList.value = res.list
    } catch (error) {
      console.error(error)
      throw error
    } finally {
      loading.value = false
    }
  }
  /**
   * 切换网格区域
   * @param region 要切换到的网格区域
   */
  const switchGridRegion = async (region: SceneTypes.SceneData) => {
    if (!region) return false

    // 如果选择的区域与当前区域相同，无需切换
    if (region.key === currentSceneData.value?.key) {
      return true
    }

    try {
      // 更新本地缓存
      sdk.uniplatSdk.global.setItem(localKeys.GridUserServiceRegionCacheKey, region.key)

      // 设置SDK场景
      sdk.setSdkScenes([{ name: sceneName, key: region.key }], '网格员入口')

      return true
    } catch (error) {
      console.error('切换网格区域失败:', error)
      return false
    }
  }

  // 重置所有状态
  const reset = () => {
    sceneList.value = null
    loading.value = false
  }

  return {
    sceneName,
    currentSceneData,
    sceneList,
    loading,
    fetchSceneList,
    switchGridRegion,
    reset
  }
})
