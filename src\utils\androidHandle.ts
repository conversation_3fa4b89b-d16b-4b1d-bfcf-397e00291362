import { message } from './message'

/**
 * Android回调函数封装类
 * 将Android原生调用封装为Promise形式
 */
class AndroidHandle {
  // 用于存储回调函数的映射
  private callbackMap: Map<string, (data: any) => void> = new Map()
  // 生成唯一ID的计数器
  private callbackCounter: number = 0

  constructor() {
    // 在全局注册回调接收器
    this.setupCallbackReceiver()
  }

  /**
   * 设置全局回调接收函数
   */
  private setupCallbackReceiver(): void {
    // 在window对象上创建接收回调的全局函数
    window.onNativeResult = (callbackId: string, data: any) => {
      console.log('data', {
        callbackId,
        data,
        type: typeof data
      })

      const callback = this.callbackMap.get(callbackId)
      if (callback) {
        callback(data)
        // 回调执行后从Map中删除
        this.callbackMap.delete(callbackId)
      } else {
        console.error('找不到对应的回调函数:', callbackId)
      }
    }
  }

  /**
   * 生成唯一的回调ID
   */
  private generateCallbackId(): string {
    return `callback_${Date.now()}_${this.callbackCounter++}`
  }

  /**
   * 创建Promise封装的Android调用
   * @param androidMethod 要调用的安卓方法名
   */
  private createPromisifiedAndroidCall<T>(
    androidMethod: 'getBaiduLocation' | 'startCamera'
  ): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      try {
        // 生成唯一的回调ID
        const callbackId = this.generateCallbackId()

        // 存储回调函数
        this.callbackMap.set(callbackId, (data: any) => {
          try {
            resolve(data as T)
          } catch (err) {
            reject(err)
          }
        })

        // 检查Android接口是否存在
        if (window.android) {
          // 使用类型断言访问方法
          const androidObj = window.android
          if (typeof androidObj[androidMethod] === 'function') {
            androidObj[androidMethod](callbackId)
          } else {
            message.showToast(`Android端不支持${androidMethod}方法`)
            reject(new Error(`Android端不支持${androidMethod}方法`))
          }
        } else {
          message.showToast('不支持的操作，请确保在Android环境中使用')
          reject(new Error('Android接口不可用'))
        }
      } catch (err) {
        reject(err)
      }
    })
  }

  /**
   * 获取百度地理位置
   * @returns Promise<位置信息>
   */
  public getBaiduLoc() {
    return this.createPromisifiedAndroidCall<{
      latitude: number
      longitude: number
      address: string
    }>('getBaiduLocation')
  }
  /**
   * 打开相机拍照
   * @returns Promise<文件信息>
   */
  public takePhoto() {
    return this.createPromisifiedAndroidCall<string>('startCamera')
  }
}

// 导出单例
export const androidHandle = new AndroidHandle()
export default androidHandle
