<template>
  <div class="flex flex-col h-screen">
    <QNavbar>
      <h1 class="text-4xl font-semibold text-primary">银行卡实名认证</h1>
    </QNavbar>
    <main class="bg-[#E5EAFF] flex-1 overflow-y-auto box-border px-11 py-10 flex">
      <div class="content bg-[#FFFFFFBF] rounded-20 flex-1 py-16 px-10">
        <section class="flex justify-between h-20">
          <div class="label text-28 text-primary-description w-64">姓名</div>
          <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
            <input
              v-model="formData.name"
              type="text"
              class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
              placeholder="请输入姓名"
            />
          </div>
        </section>
        <section class="mt-15 flex justify-between h-20">
          <div class="label text-28 text-primary-description w-64">身份证号</div>
          <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
            <input
              v-model="formData.citizenNo"
              type="text"
              class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
              placeholder="请输入身份证号"
            />
          </div>
        </section>
        <section class="mt-15 flex justify-between h-20">
          <div class="label text-28 text-primary-description w-64">银行卡号</div>
          <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
            <input
              v-model="formData.cardNo"
              type="number"
              class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
              placeholder="请输入银行卡号"
            />
          </div>
        </section>
        <section class="mt-15 flex justify-between h-20">
          <div class="label text-28 text-primary-description w-64">银行预留手机号</div>
          <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
            <input
              v-model="formData.mobile"
              type="tel"
              class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
              placeholder="请输入手机号"
            />
          </div>
        </section>
        <section class="mt-15 flex justify-between h-20 gap-6">
          <div class="flex-1 flex justify-between">
            <div class="label text-28 text-primary-description w-64">图形验证码</div>
            <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
              <input
                v-model="imgCode"
                type="text"
                class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
                placeholder="请输入图形验证码"
              />
            </div>
          </div>
          <div
            v-if="verifyImageData"
            class="rounded-2xl shrink-0 w-[192px] border border-[#747EB254] flex justify-center text-4xl overflow-hidden"
            @click="handleVerifyImage()"
          >
            <img
              class="w-full h-[74px]"
              :src="verifyImageData.img"
              alt="验证码"
            />
          </div>
        </section>
        <section class="mt-15 flex justify-between h-20 gap-6">
          <div class="flex-1 flex justify-between">
            <div class="label text-28 text-primary-description w-64">短信验证码</div>
            <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
              <input
                v-model="code"
                type="text"
                class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
                placeholder="请输入短信验证码"
              />
            </div>
          </div>
          <button
            class="rounded-2xl shrink-0 w-[192px] border border-info flex justify-center items-center text-28 text-info bg-[#5975FF1A] disabled:cursor-not-allowed disabled:opacity-50"
            :disabled="isCountingDown"
            @click="handleGetSmsCode"
          >
            {{ buttonText }}
          </button>
        </section>

        <section class="mt-20 flex justify-center">
          <QButton
            type="gradient"
            size="large"
            class="w-[454px]"
            :loading="loading"
            @click="handleAuth"
          >
            立即认证
          </QButton>
        </section>
      </div>
    </main>
  </div>
  <QModal
    v-model="showSuccessModal"
    :closeable="false"
  >
    <div class="flex flex-col items-center justify-center w-[680px] py-28">
      <img
        width="100"
        height="100"
        src="./images/auth-success.svg"
        alt=""
      />
      <p class="mt-4 text-danger font-normal text-32 text-center">恭喜您认证成功</p>
      <div class="mt-2 text-2xl text-primary">
        <span class="text-info text-30">{{ countdown }}s</span>
        后将自动前往下个页面
      </div>
    </div>
  </QModal>
</template>
<script setup lang="ts">
  import { QButton, QModal, QNavbar } from '@/components/q-components'
  import { useVerificationCode } from '@/composables/useVerificationCode'
  import { routeNameMap } from '@/router/route-name'
  import { certificationService } from '@/service/passport/certification'
  import { domainService } from '@/service/passport/domain-service'
  import { useUserStore } from '@/stores/user'
  import { isIdentityNumber, isPhone } from '@/utils'
  import { message } from '@/utils/message'
  import { reactive, ref } from 'vue'
  import { useRequest } from 'vue-request'
  import { useRouter } from 'vue-router'
  import { DEVICE_SN } from '@/service'

  const router = useRouter()
  const formData = reactive({
    name: '',
    citizenNo: '',
    cardNo: '',
    mobile: ''
    // bankName: '中国银行'
  })

  const { code, imgCode, verifyImageData, handleVerifyImage, isCountingDown, buttonText, getCode } =
    useVerificationCode()

  const handleGetSmsCode = () => {
    if (!isPhone(formData.mobile)) {
      message.showToast('手机号格式不正确')
      return
    }
    getCode(formData.mobile)
  }

  const showSuccessModal = ref(false)
  const countdown = ref(3)

  const { runAsync, loading } = useRequest(
    params => certificationService.fourElementCheck(params),
    {
      manual: true,
      onSuccess: () => {
        onGoNext()
      }
    }
  )

  const handleAuth = async () => {
    if (!isIdentityNumber(formData.citizenNo)) {
      return message.showToast('身份证号格式不正确')
    }
    await runAsync({ ...formData, verify_code: code.value })
  }

  const onGoNext = async () => {
    showSuccessModal.value = true
    // 使用store重新获取用户信息,调用bind接口
    const userStore = useUserStore()
    await Promise.all([
      userStore.fetchUserInfo(true),
      domainService<string>('bind_grid_user', {
        data: {
          device_sn: DEVICE_SN
        }
      })
    ])
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value === 0) {
        clearInterval(timer)
        router.push({ name: routeNameMap.FollowAccount })
      }
    }, 1000)
  }

  defineOptions({
    name: 'BankAuth'
  })
</script>
<style scoped></style>
