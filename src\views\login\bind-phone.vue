<template>
  <div class="flex flex-col h-screen">
    <QNavbar> </QNavbar>
    <main class="bg-[#E5EAFF] flex-1 overflow-y-auto box-border px-11 py-10 flex">
      <div class="content bg-[#FFFFFFBF] rounded-20 px-10 py-16 flex-1">
        <div class="flex flex-col items-center">
          <h1 class="text-42 font-medium text-primary">绑定手机号</h1>
          <div class="mt-9 w-full max-w-[800px]">
            <section class="flex justify-between h-20">
              <div class="label text-28 text-primary-description w-64">手机号</div>
              <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
                <input
                  v-model="mobile"
                  type="tel"
                  class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none w-full"
                  placeholder="请输入手机号"
                />
              </div>
            </section>

            <template v-if="loginType === 'code'">
              <section class="mt-15 flex justify-between gap-6 h-20">
                <div class="flex-1 flex justify-between">
                  <div class="label text-28 text-primary-description w-64">图形验证码</div>
                  <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
                    <input
                      v-model="imgCode"
                      type="text"
                      class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
                      placeholder="请输入图形验证码"
                    />
                  </div>
                </div>
                <div
                  v-if="verifyImageData"
                  class="rounded-2xl shrink-0 w-[192px] border border-[#747EB254] flex justify-center text-4xl overflow-hidden"
                  @click="handleVerifyImage()"
                >
                  <img
                    class="w-full h-[74px]"
                    :src="verifyImageData.img"
                    alt="验证码"
                  />
                </div>
              </section>

              <section class="mt-15 flex justify-between gap-6 h-20">
                <div class="flex-1 flex justify-between">
                  <div class="label text-28 text-primary-description w-64">短信验证码</div>
                  <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
                    <input
                      v-model="code"
                      type="text"
                      class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
                      placeholder="请输入短信验证码"
                    />
                  </div>
                </div>
                <button
                  class="rounded-2xl shrink-0 w-[192px] border border-info flex justify-center items-center text-28 text-info bg-[#5975FF1A] disabled:cursor-not-allowed disabled:opacity-50"
                  :disabled="isCountingDown"
                  @click="handleGetSmsCode"
                >
                  {{ buttonText }}
                </button>
              </section>
            </template>

            <template v-if="loginType === 'password'">
              <section class="mt-15 flex justify-between h-20">
                <div class="label text-28 text-primary-description w-64">密码</div>
                <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
                  <input
                    v-model="password"
                    type="password"
                    class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none w-full"
                    placeholder="请输入密码"
                  />
                </div>
              </section>
            </template>
          </div>

          <div class="mt-20">
            <QButton
              type="gradient"
              size="large"
              class="w-[500px]"
              :loading="loading"
              @click="handleNext"
            >
              下一步
            </QButton>
          </div>
        </div>
      </div>
    </main>
  </div>

  <QModal
    v-model="showSuccessModal"
    :closeable="false"
  >
    <div class="flex flex-col items-center justify-center w-[680px] py-28">
      <img
        width="100"
        height="100"
        src="./images/auth-success.svg"
        alt=""
      />
      <p class="mt-4 text-danger font-normal text-32 text-center">恭喜您绑定成功</p>
      <div class="mt-2 text-2xl text-primary">
        <span class="text-info text-30">{{ countdown }}s</span>
        后将自动前往下个页面
      </div>
    </div>
  </QModal>
</template>
<script setup lang="ts">
  import { QButton, QNavbar, QModal } from '@/components/q-components'
  import { ref } from 'vue'
  import { useVerificationCode } from '@/composables/useVerificationCode'
  import { isPhone } from '@/utils'
  import { message } from '@/utils/message'
  import { mobileBindService } from '@/service/passport/mobile-bind'
  import { useRequest } from 'vue-request'
  import { useRouter } from 'vue-router'
  import { routeNameMap } from '@/router/route-name'
  import { useUserStore } from '@/stores/user'

  const router = useRouter()
  const userStore = useUserStore()
  const loginType = ref('code')
  const mobile = ref('')
  const password = ref('')
  const loading = ref(false)
  const showSuccessModal = ref(false)
  const countdown = ref(3)

  const { code, imgCode, verifyImageData, handleVerifyImage, isCountingDown, buttonText, getCode } =
    useVerificationCode()

  // 组件挂载时获取图形验证码
  handleVerifyImage()

  const handleGetSmsCode = async () => {
    if (isCountingDown.value) return
    
    // 验证手机号
    if (!mobile.value) {
      message.showToast('请输入手机号')
      return
    }
    
    if (!isPhone(mobile.value)) {
      message.showToast('手机号格式不正确')
      return
    }
    
    // 确保有验证图片数据
    if (!verifyImageData.value) {
      await handleVerifyImage()
    }
    
    // 验证图形验证码
    if (!imgCode.value) {
      message.showToast('请输入图形验证码')
      return
    }
    
    getCode(mobile.value)
  }

  // 绑定手机号请求
  const { runAsync } = useRequest(() => mobileBindService.bind(mobile.value, code.value), {
    manual: true,
    onSuccess: () => {
      showSuccessResult()
    },
    onError: error => {
      console.error('绑定手机号失败:', error)
      message.showToast('绑定手机号失败，请重试')
      loading.value = false
    }
  })

  const handleNext = async () => {
    // 验证手机号
    if (!mobile.value) {
      message.showToast('请输入手机号')
      return
    }

    if (!isPhone(mobile.value)) {
      message.showToast('手机号格式不正确')
      return
    }

    if (loginType.value === 'code') {
      // 验证验证码
      if (!imgCode.value) {
        message.showToast('请输入图形验证码')
        return
      }

      if (!code.value) {
        message.showToast('请输入短信验证码')
        return
      }

      try {
        loading.value = true
        
        // 先检查手机号是否已经存在
        const checkResult = await mobileBindService.check(mobile.value)
        if (checkResult.result) {
          message.showToast('该手机号已被使用，请使用其他手机号')
          loading.value = false
          return
        }
        
        // 绑定手机号
        await runAsync()
      } catch (error) {
        loading.value = false
        console.error('绑定手机号过程出错:', error)
      }
    } else if (loginType.value === 'password') {
      // 验证密码
      if (!password.value) {
        message.showToast('请输入密码')
        return
      }
      message.showToast('密码登录功能暂未实现')
      loading.value = false
    }
  }

  // 显示成功结果并跳转
  const showSuccessResult = async () => {
    try {
      showSuccessModal.value = true
      // 重新获取用户信息
      await userStore.fetchUserInfo(true)

      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value === 0) {
          clearInterval(timer)
          router.push({
            name: routeNameMap.Home
          })
        }
      }, 1000)
    } catch (error) {
      console.error('跳转处理失败:', error)
      loading.value = false
    }
  }

  defineOptions({
    name: 'BindPhone'
  })
</script>
