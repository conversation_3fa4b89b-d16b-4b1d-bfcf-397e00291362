<template>
  <!--   -->
  <main class="login box-border h-screen w-screen flex flex-col items-center pt-44">
    <img
      src="/logo.svg"
      width="100px"
      height="100px"
      alt="logo"
      srcset=""
    />
    <h1 class="mt-8 font-medium text-primary text-5xl">{{ appName }}</h1>
    <!-- 非微信扫码登录 -->
    <template v-if="!useWxLogin">
      <!-- 登录表单 -->
      <section
        v-if="!useWxLogin"
        class="login-card mt-11 py-14 px-20 bg-white rounded-20 box-border w-[844px]"
      >
        <ul class="tabs flex items-center justify-around text-4xl text-primary">
          <li
            class="border-b-4 pb-2"
            :class="
              loginType === 'code' ? 'border-info font-semibold text-info' : 'border-transparent'
            "
            @click="loginType = 'code'"
          >
            验证码登录
          </li>
          <li
            class="border-b-4 pb-2"
            :class="
              loginType === 'password'
                ? 'border-info font-semibold text-info'
                : 'border-transparent'
            "
            @click="loginType = 'password'"
          >
            账号密码登录
          </li>
        </ul>

        <div class="mt-11 w-[680px]">
          <ElForm
            ref="loginFormRef"
            class="custom-form flex flex-col gap-5"
            label-position="top"
            :model="{ mobile, code, password, imgCode }"
            :rules="rules"
          >
            <ElFormItem prop="mobile">
              <ElInput
                v-model="mobile"
                type="tel"
                autocomplete="off"
                placeholder="请输入手机号"
              />
            </ElFormItem>

            <template v-if="loginType === 'code'">
              <ElFormItem prop="imgCode">
                <div class="flex gap-6 w-full">
                  <ElInput
                    v-model="imgCode"
                    placeholder="请输入图形验证码"
                  />
                  <div
                    v-if="verifyImageData"
                    class="rounded-2xl shrink-0 w-[192px] border border-[#747EB254] overflow-hidden"
                    @click="handleVerifyImage()"
                  >
                    <img
                      class="w-full h-[74px]"
                      :src="verifyImageData.img"
                      alt="验证码"
                    />
                  </div>
                </div>
              </ElFormItem>
              <ElFormItem prop="code">
                <div class="flex gap-6 w-full">
                  <ElInput
                    v-model="code"
                    type="text"
                    placeholder="请输入验证码"
                  >
                  </ElInput>
                  <button
                    type="button"
                    class="rounded-2xl shrink-0 w-[192px] border border-info flex justify-center items-center text-28 text-info bg-[#5975FF1A] disabled:cursor-not-allowed disabled:opacity-50"
                    :disabled="isCountingDown"
                    @click="handleGetCode"
                  >
                    {{ buttonText }}
                  </button>
                </div>
              </ElFormItem>
            </template>
            <template v-if="loginType === 'password'">
              <ElFormItem prop="password">
                <ElInput
                  v-model="password"
                  show-password
                  type="password"
                  placeholder="请输入密码"
                />
              </ElFormItem>
            </template>
          </ElForm>

          <div class="mt-16 w-[510px] mx-auto flex flex-col items-center">
            <div class="flex items-center text-28 font-medium">
              <ElCheckbox
                v-model="isAgreePrivacy"
                style="
                  --el-checkbox-font-size: 28px;
                  --el-checkbox-input-width: 26px;
                  --el-checkbox-input-height: 26px;
                  --el-color-primary: #21c58e;
                  --el-border-radius-small: 4px;
                "
              />
              <span
                class="text-primary-description ml-1 cursor-pointer"
                @click="isAgreePrivacy = !isAgreePrivacy"
                >已阅读并同意</span
              >
              <span
                class="text-primary cursor-pointer"
                @click="openPrivacy"
                >《数据安全保密承诺书》</span
              >
            </div>
            <QButton
              class="w-full mt-5"
              type="gradient"
              size="large"
              :loading="loginLoading"
              @click="handleCodeLogin"
            >
              登录/注册
            </QButton>

            <div class="mt-10 text-2xl text-primary-description flex items-center gap-5">
              <span class="w-[130px] h-[1px] bg-[#747EB254] relative">
                <i
                  class="absolute top-0 right-0 w-[5px] h-[5px] rounded-full transform -translate-y-1/2 bg-[#747EB254]"
                ></i>
              </span>
              <span>其他登录方式</span>
              <span class="w-[130px] h-[1px] bg-[#747EB254] relative">
                <i
                  class="absolute top-0 left-0 w-[5px] h-[5px] rounded-full transform -translate-y-1/2 bg-[#747EB254]"
                ></i>
              </span>
            </div>

            <img
              class="mt-5"
              src="@/assets/wx-icon.svg"
              width="48px"
              height="48px"
              alt="微信"
              @click="handleWxLogin"
            />
          </div>
        </div>
      </section>
      <div class="mx-auto mt-9 text-primary-description text-28">
        首次使用三方登录方式需绑定手机号
      </div>
    </template>
    <!-- 微信扫码登录 -->
    <template v-else>
      <section
        class="login-card mt-11 py-28 px-48 bg-white rounded-20 box-border w-[726px] flex-col flex items-center"
      >
        <div class="flex items-center gap-5">
          <img
            src="@/assets/wx-icon.svg"
            width="64px"
            height="64px"
            alt="微信"
          />
          <div class="text-4xl font-medium text-primary">微信授权登录</div>
        </div>

        <div class="mt-20 relative w-[208px] h-[208px] flex items-center justify-center">
          <LoadingSpinner
            v-if="isLoadingQrCode || !isQrCodeLoaded"
            class="absolute"
          />
          <img
            v-show="wxQrCodeUrl && isQrCodeLoaded"
            class="w-full h-full"
            :src="wxQrCodeUrl"
            alt="微信扫码登录"
            @load="handleQrCodeLoaded"
          />
        </div>

        <div class="mt-20 text-28 text-primary-description text-center font-normal">
          请使用微信扫一扫
          <br />
          扫描上方二维码并授权登录
        </div>
      </section>

      <div
        class="mx-auto mt-9 text-primary text-28 flex items-center gap-3"
        @click="useWxLogin = false"
      >
        验证码登录
        <img
          src="@/assets/arrow.svg"
          alt=""
          srcset=""
        />
      </div>
    </template>
  </main>
</template>
<script setup lang="ts">
  import { QButton } from '@/components/q-components'
  import { useVerificationCode } from '@/composables/useVerificationCode'
  import { routeNameMap } from '@/router/route-name'
  import { domainService } from '@/service/passport/domain-service'
  import { getRegisterAndChannelParams, loginService } from '@/service/passport/login'
  import { mobileBindService } from '@/service/passport/mobile-bind'
  import { LoginApiName } from '@/service/passport/model'
  import { PassportTokenController } from '@/service/passport/token'
  import { useUserStore } from '@/stores/user'
  import { instanceService } from '@/utils/instance'
  import { message } from '@/utils/message'
  import { sdk } from '@/utils/sdk'
  import { validationRules } from '@/utils/validation'
  import { getCacheServiceRegion } from '@/utils'
  import { ElForm, ElFormItem, ElInput, type FormInstance } from 'element-plus'
  import { ref, watch, onUnmounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import LoadingSpinner from '@/components/LoadingSpinner.vue'
  import { DEVICE_SN } from '@/service'
  import { Index } from 'uniplat-sdk'
  import { find } from 'lodash-es'

  const router = useRouter()
  const route = useRoute()

  const userStore = useUserStore()

  // 打开隐私政策
  const openPrivacy = () => {
    const currentPath = route.fullPath
    router.push({
      name: routeNameMap.Privacy,
      query: {
        redirect: currentPath
      }
    })
  }

  // 是否使用微信扫码登录
  const useWxLogin = ref(false)
  // 登录方式
  const loginType = ref('code')
  // 是否同意安全承诺书
  const isAgreePrivacy = ref(false)

  const appName = import.meta.env.VITE_APP_TITLE
  // 手机号
  const mobile = ref('')
  // 密码
  const password = ref('')
  // 登录表单实例
  const loginFormRef = ref<FormInstance>()

  const { code, imgCode, verifyImageData, handleVerifyImage, isCountingDown, buttonText, getCode } =
    useVerificationCode()

  // 获取验证码
  const handleGetCode = async () => {
    if (isCountingDown.value) return
    try {
      await loginFormRef.value?.validateField('mobile')
      await loginFormRef.value?.validateField('imgCode')
      getCode(mobile.value)
    } catch (error) {
      console.log('验证过程出错', error)
    }
  }

  // 验证规则
  const rules = ref({
    mobile: [{ required: true, message: '请输入手机号' }, validationRules.phone()],
    imgCode: [{ required: true, message: '请输入图形验证码' }],
    code: [{ required: true, message: '请输入验证码' }],
    password: [{ required: true, message: '请输入密码' }]
  })

  const loginLoading = ref(false)
  const handleCodeLogin = async () => {
    loginLoading.value = true
    try {
      PassportTokenController.clearToken()
      const validate = await loginFormRef.value?.validate()
      if (!validate) {
        return
      }

      // 检查是否同意安全承诺书
      if (!isAgreePrivacy.value) {
        message.showToast('请阅读并同意《数据安全保密承诺书》')
        loginLoading.value = false
        return
      }

      // 检查手机号是否已注册
      const hasRegistered = await mobileBindService.check(mobile.value)

      // 已注册
      let loginResult: { jwt: string; is_super: boolean } | null = null

      // 已注册，登录
      if (hasRegistered.result) {
        // 验证码登录
        if (loginType.value === 'code') {
          const _loginResult = await loginService.verifyCodeLogin(
            mobile.value,
            code.value,
            getRegisterAndChannelParams()
          )
          loginResult = _loginResult
        } else {
          // 密码登录
          const _loginResult = await sdk.uniplatSdk
            .domainService(
              sdk.subProject,
              sdk.anonymousServiceName4SystemUser,
              LoginApiName.LoginWithPassword
            )
            .post<{ jwt: string; is_super: boolean }>({
              password: password.value,
              username: mobile.value,
              client_id: import.meta.env.VITE_APP_CLIENT_ID
            })
          loginResult = _loginResult
        }
      } else {
        if (loginType.value === 'code') {
          // 未注册，注册
          const _loginResult = await loginService.register(
            mobile.value,
            code.value,
            getRegisterAndChannelParams()
          )
          loginResult = _loginResult
        } else {
          message.showToast('当前手机号未注册，请使用短信验证码登陆')
          return
        }
      }

      sdk.setup(loginResult)

      const orgList = await instanceService.getOrgList()

      sdk.orgId = orgList.list[0].id
      const scenes = (sdk.uniplatSdk.global.initData.scenes || []) as unknown as Index.Scene[]

      // 添加AppIdScene场景
      const s = find(scenes, { name: 'AppIdScene' })
      if (s) {
        s.key = import.meta.env.VITE_APP_MP_APPID
      } else {
        scenes.push({
          name: 'AppIdScene',
          key: import.meta.env.VITE_APP_MP_APPID
        })
      }

      sdk.uniplatSdk.setInitData({
        // 场景
        scenes: scenes,
        // 应用ID
        orgId: sdk.orgId,
        // 应用实例ID
        xid: sdk.orgId
      })

      await sdk.uniplatSdk.configurationApi.changeTokenWithXid(orgList.list[0].id + '')

      await loginService.refreshLogin()

      // 加载用户信息到store中
      const userInfo = await userStore.fetchUserInfo(true)
      if (userInfo.citizen_verified) {
        await domainService('bind_grid_user', {
          data: {
            device_sn: DEVICE_SN
          }
        })
      }

      const redirect = route.query.redirect as string
      if (redirect) {
        router.push(redirect)
      } else {
        router.push({ name: routeNameMap.Home })
      }
    } catch (error) {
      console.error('登录失败:', error)
    } finally {
      loginLoading.value = false
    }
  }

  // 微信登录相关
  const isLoadingQrCode = ref(false)
  const isQrCodeLoaded = ref(false)
  const wxQrCodeUrl = ref('')
  const wxLoginCode = ref('')
  let wxLoginStatusTimer: number | null = null

  // 清除微信登录状态轮询
  const clearWxLoginStatusTimer = () => {
    if (wxLoginStatusTimer) {
      clearInterval(wxLoginStatusTimer)
      wxLoginStatusTimer = null
    }
  }

  // 开始轮询微信登录状态
  const startWxLoginStatusPolling = () => {
    // 先清除可能存在的定时器
    clearWxLoginStatusTimer()

    // 开始轮询
    wxLoginStatusTimer = setInterval(async () => {
      try {
        const res = await loginService.getWxLoginStatus(wxLoginCode.value)
        if (res.status === 1) {
          // 登录成功，清除轮询
          clearWxLoginStatusTimer()
          // 使用token登录
          sdk.uniplatSdk.loginByToken({
            token: res.token
          })
          // 获取组织列表
          const orgList = await instanceService.getOrgList()
          sdk.orgId = orgList.list[0].id
          sdk.orgId &&
            sdk.uniplatSdk.setInitData({
              orgId: sdk.orgId,
              xid: sdk.orgId
            })
          let id = getCacheServiceRegion()
          if (id) {
            sdk.setSdkScenes([{ name: 'GridRegionScene', key: id }], '网格员入口')
          }
          await sdk.uniplatSdk.configurationApi.changeTokenWithXid(orgList.list[0].id + '')
          await loginService.refreshLogin()
          // 加载用户信息到store中
          const userInfo = await userStore.fetchUserInfo(true)
          if (userInfo.citizen_verified) {
            sdk.setSdkScenes(
              [{ name: 'AppIdScene', key: import.meta.env.VITE_APP_MP_APPID }],
              '居民就业服务客户端'
            )
            await domainService('bind_grid_user', {
              data: {
                device_sn: DEVICE_SN
              }
            })
          }
          // 跳转
          const redirect = route.query.redirect as string
          if (redirect) {
            router.push(redirect)
          } else {
            router.push({ name: routeNameMap.Home })
          }
        }
      } catch (error) {
        console.error('检查微信登录状态失败:', error)
      }
    }, 1000)
  }

  const handleQrCodeLoaded = () => {
    isQrCodeLoaded.value = true
  }

  const handleWxLogin = async () => {
    useWxLogin.value = true
    isLoadingQrCode.value = true
    isQrCodeLoaded.value = false
    try {
      const res = await loginService.getWxLoginUrl()
      wxQrCodeUrl.value = res.qr_file_path
      wxLoginCode.value = res.code
      // 开始轮询登录状态
      startWxLoginStatusPolling()
    } catch (error) {
      console.log('获取二维码失败', error)
      message.showToast('获取二维码失败，请重试')
      useWxLogin.value = false
    } finally {
      isLoadingQrCode.value = false
    }
  }

  // 监听切换登录方式
  watch(useWxLogin, newVal => {
    if (!newVal) {
      clearWxLoginStatusTimer()
    }
  })

  // 组件卸载时清除轮询
  onUnmounted(() => {
    clearWxLoginStatusTimer()
  })

  defineOptions({
    name: 'Login'
  })
</script>
<style scoped>
  .login {
    background-image: url('@/assets/home-bg.svg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
  }

  :deep(.el-checkbox__inner::after) {
    width: 6px;
    left: 8px;
    height: 16px;
  }

  .login-card {
    backdrop-filter: blur(40px);
    box-shadow: 0px 5px 15px 0px #c9d7e580;
  }
</style>
