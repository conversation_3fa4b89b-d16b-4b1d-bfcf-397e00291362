<template>
  <div class="flex flex-col h-screen">
    <QNavbar> </QNavbar>
    <main class="page flex-1 overflow-y-auto box-border px-11 py-10 flex">
      <div class="content bg-[#FFFFFFBF] rounded-20 flex-1 py-16 px-10">
        <h1 class="text-4xl font-semibold text-primary text-center">设置登录密码</h1>

        <p
          v-show="step === 2"
          class="mt-5 font-normal text-28 text-primary-description text-center"
        >
          登录密码长度位8-16位，可为数字/大小写字母或数字和大小写字母的组合
        </p>
        <template v-if="step === 1">
          <section class="mt-15 flex justify-between">
            <div class="label text-28 text-primary-description w-64">手机号</div>
            <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
              <input
                v-model="mobile"
                type="tel"
                class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
                placeholder="请输入手机号"
              />
            </div>
          </section>
          <section class="mt-15 flex justify-between gap-6">
            <div class="flex-1 flex justify-between">
              <div class="label text-28 text-primary-description w-64">图形验证码</div>
              <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
                <input
                  v-model="imgCode"
                  type="text"
                  class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
                  placeholder="请输入图形验证码"
                />
              </div>
            </div>
            <div
              v-if="verifyImageData"
              class="rounded-2xl shrink-0 w-[192px] border border-[#747EB254] flex justify-center items-center text-4xl cursor-pointer overflow-hidden"
              @click="handleVerifyImage()"
            >
              <img
                class="w-full h-[74px]"
                :src="verifyImageData.img"
                alt="验证码"
              />
            </div>
            <div
              v-else
              class="rounded-2xl shrink-0 w-[192px] border border-[#747EB254] flex justify-center items-center text-4xl cursor-pointer"
              @click="handleVerifyImage()"
            >
              点击获取
            </div>
          </section>
          <section class="mt-15 flex justify-between gap-6">
            <div class="flex-1 flex justify-between">
              <div class="label text-28 text-primary-description">短信验证码</div>
              <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
                <input
                  v-model="code"
                  type="text"
                  class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
                  placeholder="请输入短信验证码"
                />
              </div>
            </div>
            <button
              type="button"
              class="rounded-2xl shrink-0 w-[192px] border border-info flex justify-center items-center text-28 text-info bg-[#5975FF1A] disabled:cursor-not-allowed disabled:opacity-50"
              :disabled="isCountingDown"
              @click="handleGetCode"
            >
              {{ buttonText }}
            </button>
          </section>
        </template>
        <template v-if="step === 2">
          <section class="mt-15 flex items-center justify-between">
            <div class="label text-28 text-primary-description w-64">登录密码</div>
            <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
              <input
                v-model="password"
                type="password"
                class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
                placeholder="请输入8-16位登录密码"
              />
            </div>
          </section>
          <section class="mt-15 flex items-center justify-between">
            <div class="label text-28 text-primary-description w-64">再次输入</div>
            <div class="border-b border-[#747EB254] flex-1 flex justify-end pb-5">
              <input
                v-model="confirmPassword"
                type="password"
                class="text-right text-28 placeholder:text-[#747EB280] focus-visible:outline-none"
                placeholder="请确认登录密码"
              />
            </div>
          </section>
        </template>

        <section class="mt-20 flex justify-center">
          <QButton
            type="gradient"
            size="large"
            class="w-[454px]"
            :loading="loading"
            @click="handleNext"
          >
            {{ step === 1 ? '下一步' : '确定' }}
          </QButton>
        </section>
      </div>
    </main>
  </div>
</template>
<script setup lang="ts">
  import { QButton, QNavbar } from '@/components/q-components'
  import { useVerificationCode } from '@/composables/useVerificationCode'
  import router from '@/router'
  import { resetPasswordService } from '@/service/passport/reset-password'
  import { verifyService } from '@/service/passport/verify'
  import { message } from '@/utils/message'
  import { validationRules } from '@/utils/validation'
  import { ElNotification } from 'element-plus'
  import { ref } from 'vue'

  const step = ref(0)
  const mobile = ref('')
  const password = ref('')
  const confirmPassword = ref('')
  const loading = ref(false)

  // 获取验证码相关
  const { code, imgCode, verifyImageData, handleVerifyImage, isCountingDown, buttonText, getCode } =
    useVerificationCode()

  // 验证手机号
  const validateMobile = () => {
    if (!mobile.value) {
      message.showToast('请输入手机号')
      return false
    }
    if (!validationRules.phone().pattern.test(mobile.value)) {
      message.showToast('请输入正确的手机号码')
      return false
    }
    return true
  }

  // 验证图形验证码
  const validateImgCode = () => {
    if (!imgCode.value) {
      message.showToast('请输入图形验证码')
      return false
    }
    return true
  }

  // 验证短信验证码
  const validateCode = () => {
    if (!code.value) {
      message.showToast('请输入短信验证码')
      return false
    }
    return true
  }

  // 验证密码
  const validatePassword = () => {
    if (!password.value) {
      message.showToast('请输入密码')
      return false
    }
    if (password.value.length < 8 || password.value.length > 16) {
      message.showToast('密码长度为8-16位')
      return false
    }
    if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(password.value)) {
      message.showToast('密码需要包含字母和数字')
      return false
    }
    return true
  }

  // 验证确认密码
  const validateConfirmPassword = () => {
    if (!confirmPassword.value) {
      message.showToast('请再次输入密码')
      return false
    }
    if (confirmPassword.value !== password.value) {
      message.showToast('两次输入的密码不一致')
      return false
    }
    return true
  }

  // 获取验证码
  const handleGetCode = async () => {
    if (isCountingDown.value) return

    if (!validateMobile() || !validateImgCode()) {
      return
    }

    getCode(mobile.value)
  }

  // 点击下一步或确定按钮
  const handleNext = async () => {
    loading.value = true
    try {
      if (step.value === 1) {
        // 验证第一步表单
        if (!validateMobile() || !validateImgCode() || !validateCode()) {
          loading.value = false
          return
        }

        // 验证手机号和验证码是否匹配
        const verifyResult = await verifyService.verifyMobile(mobile.value, code.value.toString())

        if (verifyResult.check_result) {
          step.value = 2
        } else {
          message.showToast('验证码错误，请重新输入')
        }
      } else {
        // 验证第二步表单
        if (!validatePassword() || !validateConfirmPassword()) {
          loading.value = false
          return
        }

        // 提交重置密码请求
        await resetPasswordService.resetPassword(
          mobile.value,
          code.value.toString(),
          password.value
        )

        ElNotification({
          title: '密码设置成功',
          message: '下次请使用新密码登录',
          type: 'success'
        })

        router.back()
      }
    } catch (error: any) {
      if (error.message) {
        message.showToast(error.message)
      } else {
        message.showToast('操作失败，请重试')
      }
      console.error('操作失败', error)
    } finally {
      loading.value = false
    }
  }

  defineOptions({
    name: 'SetPassword'
  })
</script>
<style scoped>
  .page {
    background-image: url('@/assets/home-bg.svg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
  }
</style>
