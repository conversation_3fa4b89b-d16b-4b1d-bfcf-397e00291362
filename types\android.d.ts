// Android WebView 接口定义
declare global {
  interface Window {
    android?: {
      getDeviceInfo(): string
      getDeviceId(): string
      getCurrentVersion(): string
      jsToast(message: string): void
      reload(): void
      exit(): void
      openApp(packageName: string): void
      getLocationInfo(): string
      getBaiduLocation(callbackId: string): void
      getIotCardInfo(): {
        simState: string | '正常'
        simOperator: string
        simOperatorName: string | '中国移动'
        iccid: string
        line1Number: string
        isRoaming: boolean
        networkType: number
        imsi: string
      }
      startCamera(callbackId: string): void
    }
    onNativeResult: (callbackId: string, data: any) => void
  }
}

export {}
